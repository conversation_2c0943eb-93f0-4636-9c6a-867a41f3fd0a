<?php

namespace App\Services\Facebook;

use Log;
use FacebookAds\Api;
use ReflectionClass;
use App\Models\ApiKey;
use GuzzleHttp\Client;
use FacebookAds\Object\Campaign;
use FacebookAds\Object\AdAccount;
use FacebookAds\Object\AdsInsights;
use Illuminate\Support\Facades\Cache;
use FacebookAds\Object\Fields\AdFields;
use FacebookAds\Object\Fields\AdSetFields;
use FacebookAds\Object\Fields\CampaignFields;
use FacebookAds\Object\Fields\InsightsResultFields;

class FacebookAdService
{
    protected $appId;
    protected $appSecret;
    protected $accessToken;
    protected $config;
    // protected $accountId;

    public function __construct()
    {
        // Load configuration from database
        try {
            $this->loadConfigFromDatabase();

            if ($this->isConfigured()) {
                Api::init($this->appId, $this->appSecret, $this->accessToken, false);
            }
        } catch (\Exception $e) {
            // Service akan tetap bisa di-instantiate, tapi method isConfigured() akan return false
            Log::warning('Facebook service initialized without proper configuration: ' . $e->getMessage());
        }
    }

    /**
     * Load Facebook configuration from database
     */
    protected function loadConfigFromDatabase()
    {
        try {
            // Cache configuration for 1 hour
            $this->config = Cache::remember('facebook_config', 3600, function () {
                return ApiKey::first();
            });

            if (
                $this->config &&
                !empty($this->config->facebook_app_id) &&
                !empty($this->config->facebook_app_secret) &&
                !empty($this->config->facebook_access_token)
            ) {

                $this->appId = $this->config->facebook_app_id;
                $this->appSecret = $this->config->facebook_app_secret;
                $this->accessToken = $this->config->facebook_access_token;
            } else {
                throw new \Exception('Facebook credentials are not properly configured in database');
            }
        } catch (\Exception $e) {
            Log::error('Facebook configuration load error: ' . $e->getMessage());
            throw new \Exception('Facebook configuration error: ' . $e->getMessage());
        }
    }

    /**
     * Check if Facebook is configured
     */
    public function isConfigured()
    {
        return !empty($this->appId) && !empty($this->appSecret) && !empty($this->accessToken);
    }

    /**
     * Test connection to Facebook API
     */
    public function testConnection()
    {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'message' => 'Facebook credentials are not configured'
            ];
        }

        try {
            // Re-initialize API untuk memastikan kredensial terbaru
            Api::init($this->appId, $this->appSecret, $this->accessToken, false);

            // Test dengan endpoint sederhana - cek access token
            $fields = ['id', 'name'];
            $user = new \FacebookAds\Object\User('me');
            $user->read($fields);

            return [
                'success' => true,
                'message' => 'Facebook connection successful',
                'data' => [
                    'user_id' => $user->id ?? 'Unknown',
                    'user_name' => $user->name ?? 'Unknown',
                    'app_id' => $this->appId
                ]
            ];
        } catch (\FacebookAds\Exception\AuthorizationException $e) {
            Log::error('Facebook authorization error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Invalid access token or insufficient permissions. Please check your Facebook credentials.'
            ];
        } catch (\FacebookAds\Exception\AuthenticationException $e) {
            Log::error('Facebook authentication error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Authentication failed. Please check your App ID and App Secret.'
            ];
        } catch (\FacebookAds\Exception\PermissionException $e) {
            Log::error('Facebook permission error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Permission denied. Your access token may not have required permissions.'
            ];
        } catch (\FacebookAds\Exception\RequestException $e) {
            Log::error('Facebook request error: ' . $e->getMessage());

            $errorData = $e->getErrorData();
            $errorMessage = 'Facebook API error';

            if (isset($errorData['error']['message'])) {
                $errorMessage = $errorData['error']['message'];
            }

            return [
                'success' => false,
                'message' => $errorMessage
            ];
        } catch (\Exception $e) {
            Log::error('Facebook test connection error: ' . $e->getMessage());

            // Handle specific error messages
            $message = $e->getMessage();
            if (strpos($message, 'cURL error') !== false) {
                return [
                    'success' => false,
                    'message' => 'Network connection error. Please check your internet connection.'
                ];
            } elseif (strpos($message, 'Invalid application ID') !== false) {
                return [
                    'success' => false,
                    'message' => 'Invalid App ID. Please check your Facebook App ID.'
                ];
            }

            // Try alternative simple test
            Log::info('Facebook SDK test failed, trying simple HTTP test');
            return $this->simpleHttpTest();
        }
    }

    /**
     * Simple HTTP test for Facebook Graph API
     */
    private function simpleHttpTest()
    {
        try {
            $client = new \GuzzleHttp\Client();

            // Simple test to Graph API
            $url = "https://graph.facebook.com/me?access_token=" . $this->accessToken;

            $response = $client->get($url, [
                'timeout' => 10,
                'connect_timeout' => 5
            ]);

            if ($response->getStatusCode() == 200) {
                $data = json_decode($response->getBody()->getContents(), true);

                if (isset($data['id'])) {
                    return [
                        'success' => true,
                        'message' => 'Facebook connection successful (simple test)',
                        'data' => [
                            'user_id' => $data['id'] ?? 'Unknown',
                            'user_name' => $data['name'] ?? 'Unknown',
                            'app_id' => $this->appId
                        ]
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'Invalid response from Facebook API'
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => 'HTTP Error: ' . $response->getStatusCode()
                ];
            }
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $response = $e->getResponse();
            $statusCode = $response->getStatusCode();
            $body = json_decode($response->getBody()->getContents(), true);

            $errorMessage = 'Facebook API error';
            if (isset($body['error']['message'])) {
                $errorMessage = $body['error']['message'];
            }

            return [
                'success' => false,
                'message' => $errorMessage . ' (HTTP ' . $statusCode . ')'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Simple test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Refresh configuration cache
     */
    public function refreshConfig()
    {
        Cache::forget('facebook_config');
        $this->loadConfigFromDatabase();

        // Re-initialize API with new credentials
        if ($this->isConfigured()) {
            Api::init($this->appId, $this->appSecret, $this->accessToken, false);
        }
    }

    public function getDataCampaigns($accountId)
    {
        // Check if Facebook is configured
        if (!$this->isConfigured()) {
            return [
                'error' => 'Facebook API is not configured',
                'message' => 'Please configure Facebook credentials in API Key settings'
            ];
        }

        // return $accountId;
        try {
            // Validate account ID
            if (empty($accountId)) {
                return ['error' => 'Account ID is missing'];
            }

            // Start Get campaign details
            $account = new AdAccount($accountId);
            $fields = [
                CampaignFields::ID,
                CampaignFields::NAME,
                CampaignFields::STATUS,
                CampaignFields::BUDGET_REMAINING,
                CampaignFields::BUYING_TYPE,
                CampaignFields::CREATED_TIME,
                CampaignFields::DAILY_BUDGET,
                CampaignFields::STOP_TIME,
                CampaignFields::BID_STRATEGY,
                CampaignFields::CAMPAIGN_GROUP_ACTIVE_TIME,
                CampaignFields::PRIMARY_ATTRIBUTION,
                CampaignFields::SOURCE_CAMPAIGN,
                CampaignFields::START_TIME,
                CampaignFields::ADLABELS,
            ];

            $params = [
                'date_preset' => 'maximum',
                // 'level' => 'ad',
                'limit' => '1000',
                // 'insights_selected_metrics' => 'cost_per_result',
            ];


            $campaigns = $account->getCampaigns($fields, $params); //CampaignFields.php
            // return "sini";

            $resultData = [];
            foreach ($campaigns as $campaign) {
                $campaignData = [
                    'id_kampanye' => $campaign->{CampaignFields::ID},
                    'nama_kampanye' => $campaign->{CampaignFields::NAME},
                    'status' => $campaign->{CampaignFields::STATUS},
                    'anggaran_tersisa' => $campaign->{CampaignFields::BUDGET_REMAINING},
                    'jenis_pembelian' => $campaign->{CampaignFields::BUYING_TYPE},
                    'waktu_dibuat' => date('d-m-Y', strtotime($campaign->{CampaignFields::CREATED_TIME})),
                    'anggaran_harian' => $campaign->{CampaignFields::DAILY_BUDGET},
                    'waktu_berhenti' => date('d-m-Y', strtotime($campaign->{CampaignFields::STOP_TIME})),
                    'strategi_bid' => $campaign->{CampaignFields::BID_STRATEGY} ?? null,
                    'waktu_kampanye_aktif' => $campaign->{CampaignFields::CAMPAIGN_GROUP_ACTIVE_TIME} ?? null,
                    'atribusi_utama' => $campaign->{CampaignFields::PRIMARY_ATTRIBUTION} ?? null,
                    'asal_kampanye' => $campaign->{CampaignFields::SOURCE_CAMPAIGN} ?? null,
                    'waktu_mulai' => $campaign->{CampaignFields::START_TIME} ?? null,
                    'adlabels' => isset($campaign->{CampaignFields::ADLABELS}) && is_array($campaign->{CampaignFields::ADLABELS})
                        ? implode(', ', array_map(function ($label) {
                            return $label['name']; // atau $label['id'] jika ingin menampilkan id
                        }, $campaign->{CampaignFields::ADLABELS}))
                        : null,
                ];

                $resultData[] = $campaignData;
            }

            return $resultData;
            // return $this->getDataAdSet($account);
            // return $this->getDataAd($account);
            // return $this->getDataInsights($account);
            // return $this->getDataInsightsv2($account);
            // return $this->getDataInsightsFileds($account);

        } catch (\Exception $e) {
            Log::error('Facebook Ads API Error (getDataCampaigns): ' . $e->getMessage());
            return [
                'success' => false,
                'error' => true,
                'message' => 'An error occurred while fetching campaigns: ' . $e->getMessage(),
            ];
        }

    }

    public function getDataAdSet($accountId)
    {
        // public function getDataAdSet($account){
        // return $account;
        // $accountId = 'act_'.'****************';
        $account = new AdAccount($accountId);
        // Start fetching ad sets and ads
        $adSetFields = [
            AdSetFields::NAME,
            AdSetFields::ACCOUNT_ID,
            AdSetFields::BUDGET_REMAINING,
            AdSetFields::CAMPAIGN_ID,
            AdSetFields::DAILY_BUDGET,
                // AdSetFields::DAILY_MIN_SPEND_TARGET,
                // AdSetFields::START_TIME,
            AdSetFields::ATTRIBUTION_SPEC,
                // AdSetFields::CAMPAIGN_ACTIVE_TIME,
            AdSetFields::CAMPAIGN_ATTRIBUTION,
            AdSetFields::ID,
            AdSetFields::STATUS,
            AdSetFields::CREATED_TIME,
            AdSetFields::END_TIME,
        ];

        // $adFields = [
        //     AdFields::NAME,
        //     AdFields::STATUS,
        //     AdFields::AD_ACTIVE_TIME,
        //     AdFields::AD_REVIEW_FEEDBACK,
        //     AdFields::AD_SCHEDULE_END_TIME,
        //     AdFields::AD_SCHEDULE_START_TIME,
        //     AdFields::ID,
        // ];

        $params = [
            'date_preset' => 'maximum',
            'level' => 'ad',
            'limit' => 1000 // max per page; Facebook may still return less depending on data size
        ];

        $adsets = $account->getAdSets($adSetFields, $params); //AdSetFields.php

        $AdSets = [];
        foreach ($adsets as $adset) {
            $adsetData = [
                'nama_set_iklan' => $adset->{AdSetFields::NAME} ?? null,
                'id_akun' => $adset->{AdSetFields::ACCOUNT_ID} ?? null,
                'sisa_anggaran' => $adset->{AdSetFields::BUDGET_REMAINING} ?? null,
                'id_kampanye' => $adset->{AdSetFields::CAMPAIGN_ID} ?? null,
                'anggaran_harian' => $adset->{AdSetFields::DAILY_BUDGET} ?? null,
                // 'target_pengeluaran_minimum_harian' => $adset->{AdSetFields::DAILY_MIN_SPEND_TARGET} ?? null,
                // 'waktu_mulai' => date('d-m-Y', strtotime($adset->{AdSetFields::START_TIME})) ?? null,
                'spek_atribusi' => $adset->{AdSetFields::ATTRIBUTION_SPEC} ?? null,
                // 'waktu_kampanye_habis' => $adset->{AdSetFields::CAMPAIGN_ACTIVE_TIME} ?? null,
                'atribusi_kampanye' => $adset->{AdSetFields::CAMPAIGN_ATTRIBUTION} ?? null,
                'id' => $adset->{AdSetFields::ID} ?? null,
                'status' => $adset->{AdSetFields::STATUS} ?? null,
                'waktu_dibuat' => date('Y-m-d', strtotime($adset->{AdSetFields::CREATED_TIME})) ?? null,
                'waktu_selesai' => date('Y-m-d', strtotime($adset->{AdSetFields::END_TIME})) ?? null,
                // 'iklan' => [] // Tempat untuk menyimpan data iklan yang terkait dengan ad set ini
            ];

            // $ads = $account->getAds($adFields, $params); //AdFields.php
            // foreach ($ads as $ad) {
            //     $adsetData['iklan'][] = [
            //         'id_iklan' => $ad->{AdFields::ID} ?? null,
            //         'nama_iklan' => $ad->{AdFields::NAME} ?? null,
            //         'status_penayangan' => $ad->{AdFields::STATUS} ?? null,
            //         'waktu_aktif_iklan' => $ad->{AdFields::AD_ACTIVE_TIME} ?? null,
            //         'umpan_balik_review_iklan' => $ad->{AdFields::AD_REVIEW_FEEDBACK} ?? null,
            //         'jadwal_akhir_iklan' => $ad->{AdFields::AD_SCHEDULE_END_TIME} ?? null,
            //         'jadwal_mulai_iklan' => $ad->{AdFields::AD_SCHEDULE_START_TIME} ?? null,
            //     ];
            $AdSets[] = $adsetData;
            // }

        }
        return $AdSets;
    }

    // public function getDataAd($account){
    public function getDataAd($accountId)
    {
        // return $account;
        // $accountId = 'act_'.'****************';
        $account = new AdAccount($accountId);
        // Start fetching ad sets and ads
        // $adSetFields = [
        //     AdSetFields::NAME,
        //     AdSetFields::ACCOUNT_ID,
        //     AdSetFields::BUDGET_REMAINING,
        //     AdSetFields::CAMPAIGN_ID,
        //     // AdSetFields::CREATED_TIME,
        //     AdSetFields::DAILY_BUDGET,
        //     // AdSetFields::DAILY_MIN_SPEND_TARGET,
        //     // AdSetFields::END_TIME,
        //     // AdSetFields::START_TIME,
        //     AdSetFields::ATTRIBUTION_SPEC,
        //     // AdSetFields::CAMPAIGN_ACTIVE_TIME,
        //     AdSetFields::CAMPAIGN_ATTRIBUTION,
        //     AdSetFields::ID,
        //     AdSetFields::STATUS
        // ];

        $adFields = [
            AdFields::NAME,
            AdFields::STATUS,
            AdFields::AD_ACTIVE_TIME,
            AdFields::AD_REVIEW_FEEDBACK,
            AdFields::AD_SCHEDULE_END_TIME,
            AdFields::AD_SCHEDULE_START_TIME,
            AdFields::ID,
            AdFields::CREATED_TIME,
            AdFields::UPDATED_TIME,
        ];

        $params = [
            'date_preset' => 'maximum',
            'level' => 'ad',
            'limit' => 200 // max per page; Facebook may still return less depending on data size
        ];

        // $adsets = $account->getAdSets($adSetFields, $params); //AdSetFields.php

        $AdSets = [];
        // foreach ($adsets as $adset) {
        //     $adsetData = [
        //         'nama_set_iklan' => $adset->{AdSetFields::NAME} ?? null,
        //         'id_akun' => $adset->{AdSetFields::ACCOUNT_ID} ?? null,
        //         'sisa_anggaran' => $adset->{AdSetFields::BUDGET_REMAINING} ?? null,
        //         'id_kampanye' => $adset->{AdSetFields::CAMPAIGN_ID} ?? null,
        //         // 'waktu_dibuat' => date('d-m-Y', strtotime($adset->{AdSetFields::CREATED_TIME})) ?? null,
        //         'anggaran_harian' => $adset->{AdSetFields::DAILY_BUDGET} ?? null,
        //         // 'target_pengeluaran_minimum_harian' => $adset->{AdSetFields::DAILY_MIN_SPEND_TARGET} ?? null,
        //         // 'waktu_selesai' => date('d-m-Y', strtotime($adset->{AdSetFields::END_TIME})) ?? null,
        //         // 'waktu_mulai' => date('d-m-Y', strtotime($adset->{AdSetFields::START_TIME})) ?? null,
        //         'spek_atribusi' => $adset->{AdSetFields::ATTRIBUTION_SPEC} ?? null,
        //         // 'waktu_kampanye_habis' => $adset->{AdSetFields::CAMPAIGN_ACTIVE_TIME} ?? null,
        //         'atribusi_kampanye' => $adset->{AdSetFields::CAMPAIGN_ATTRIBUTION} ?? null,
        //         'id' => $adset->{AdSetFields::ID} ?? null,
        //         'status' => $adset->{AdSetFields::STATUS} ?? null,
        //         // 'iklan' => [] // Tempat untuk menyimpan data iklan yang terkait dengan ad set ini
        //     ];

        $ads = $account->getAds($adFields, $params); //AdFields.php
        foreach ($ads as $ad) {
            $adsetData = [
                'id_iklan' => $ad->{AdFields::ID} ?? null,
                'nama_iklan' => $ad->{AdFields::NAME} ?? null,
                'status_penayangan' => $ad->{AdFields::STATUS} ?? null,
                'waktu_aktif_iklan' => $ad->{AdFields::AD_ACTIVE_TIME} ?? null,
                'umpan_balik_review_iklan' => $ad->{AdFields::AD_REVIEW_FEEDBACK} ?? null,
                'jadwal_akhir_iklan' => $ad->{AdFields::AD_SCHEDULE_END_TIME} ?? null,
                'jadwal_mulai_iklan' => $ad->{AdFields::AD_SCHEDULE_START_TIME} ?? null,
                'waktu_dibuat' => date('d-m-Y', strtotime($ad->{AdFields::CREATED_TIME})) ?? null,
                'waktu_selesai' => date('d-m-Y', strtotime($ad->{AdFields::UPDATED_TIME})) ?? null
            ];
            $AdSets[] = $adsetData;
        }

        // }
        return $AdSets;
    }

    public function getDataInsights($accountId)
    {
        // public function getDataInsights($account) {
        // $accountId = 'act_'.'****************';
        $account = new AdAccount($accountId);
        $insightFields = [
            'account_id', // ID akun iklan
            'account_name', // Nama akun iklan
            'action_values', // Nilai konversi dari semua aksi (misalnya pembelian)
            'actions', // Semua jenis tindakan yang dilakukan user terhadap iklan
            'ad_id', // ID iklan
            'ad_name', // Nama iklan
            'adset_id', // ID ad set
            'adset_name', // Nama ad set
            'buying_type', // Jenis pembelian iklan (misalnya: AUCTION, REACH_AND_FREQUENCY)
            'campaign_id', // ID kampanye
            'campaign_name', // Nama kampanye
            'clicks', // Jumlah total klik
            'conversions', // Jumlah total konversi
            'conversion_rate_ranking', // Peringkat rasio konversi dibanding iklan lain
            'cpc', // Biaya per klik
            'cpm', // Biaya per 1000 tayangan
            'cpp', // Biaya per hasil (Cost Per Purchase/Lead/etc.)
            'ctr', // Click-Through Rate (persentase klik dari tayangan)
            'date_start', // Tanggal mulai laporan
            'date_stop', // Tanggal akhir laporan
            'frequency', // Rata-rata berapa kali audiens melihat iklan
            'impressions', // Total tayangan iklan
            'reach', // Jumlah orang unik yang melihat iklan
            'spend', // Total biaya yang dikeluarkan
            'quality_ranking', // Peringkat kualitas iklan dibanding kompetitor
            'purchase_roas',        // ROAS dari semua pembelian
        ];


        $params = [
            'date_preset' => 'maximum',
            'level' => 'ad',
            'limit' => 1000 // max per page; Facebook may still return less depending on data size
            // 'level' => 'campaign',      // Tingkat kampanye
            // 'date_preset' => 'last_30d',// Rentang waktu: 30 hari terakhir
            // 'action_attribution_windows' => ['28d_click', '1d_view'], // Jendela atribusi
            // 'time_increment' => 'all_days', // Satu hasil untuk seluruh periode
            // 'use_account_attribution_setting' => true, // Gunakan pengaturan atribusi akun
        ];

        try {
            $insights = $account->getInsights($insightFields, $params);
            $insightData = [];

            // do {
            foreach ($insights as $insight) {
                $insightData[] = [
                    'id_akun' => $insight->account_id ?? null, // ID akun iklan
                    'nama_akun' => $insight->account_name ?? null, // Nama akun iklan
                    'aksi' => $insight->actions ?? null, // Semua jenis tindakan yang dilakukan user terhadap iklan
                    'id_iklan' => $insight->ad_id ?? null, // ID iklan
                    'nama_iklan' => $insight->ad_name ?? null, // Nama iklan
                    'id_set_iklan' => $insight->adset_id ?? null, // ID ad set
                    'nama_set_iklan' => $insight->adset_name ?? null, // Nama ad set
                    'jenis_pembelian' => $insight->buying_type ?? null, // Jenis pembelian iklan (AUCTION, dll.)
                    'id_kampanye' => $insight->campaign_id ?? null, // ID kampanye
                    'nama_kampanye' => $insight->campaign_name ?? null, // Nama kampanye
                    'klik' => $insight->clicks ?? null, // Jumlah total klik
                    'konversi' => $insight->conversions ?? null, // Jumlah total konversi
                    'peringkat_rasio_konversi' => $insight->conversion_rate_ranking ?? null, // Dibanding iklan lain
                    'biaya_per_klik' => $insight->cpc ?? null, // Biaya per klik
                    'biaya_per_1000_tayangan' => $insight->cpm ?? null, // Biaya per 1000 tayangan
                    'biaya_per_hasil' => $insight->cpp ?? null, // Cost per result
                    'ctr' => $insight->ctr ?? null, // Click-through rate
                    'tanggal_mulai_laporan' => $insight->date_start ?? null, // Tanggal awal laporan
                    'tanggal_akhir_laporan' => $insight->date_stop ?? null, // Tanggal akhir laporan
                    'frekuensi' => $insight->frequency ?? null, // Rata-rata jumlah tayangan per orang
                    'impresi' => $insight->impressions ?? null, // Jumlah total tayangan
                    'jangkauan' => $insight->reach ?? null, // Jumlah orang unik yang melihat iklan
                    'biaya_dibelanjakan' => $insight->spend ?? null, // Total biaya dikeluarkan
                    'peringkat_kualitas' => $insight->quality_ranking ?? null, // Peringkat kualitas dibanding iklan lain
                ];
            }
            // } while ($insights = $insights->getNextPage());

            // Sorting berdasarkan id_kampanye (desc)
            usort($insightData, function ($a, $b) {
                return $b['id_kampanye'] <=> $a['id_kampanye'];
            });

            return $insightData;
            // return response()->json([
            //     // 'count' => count($insightData),
            //     'data' => $insightData
            // ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
    public function getDataInsightscoba($accountId)
    {
        try {
            // return $accountId;
            // $accountId = 'act_'.'****************';
            $account = new AdAccount($accountId);
            // Daftar kolom yang relevan untuk pembelian situs web
            $insightFields = [
                'reach',                 // Jangkauan
                'account_currency', // Mata uang akun iklan
                'account_id', // ID akun iklan
                'account_name', // Nama akun iklan
                'action_values', // Nilai konversi dari semua aksi (misalnya pembelian)
                'actions', // Semua jenis tindakan yang dilakukan user terhadap iklan
                'ad_click_actions', // Jumlah klik pada iklan
                'ad_id', // ID iklan
                'ad_impression_actions', // Tindakan yang terjadi saat tayangan iklan
                'ad_name', // Nama iklan
                'adset_end', // Tanggal akhir ad set
                'adset_id', // ID ad set
                'adset_name', // Nama ad set
                'adset_start', // Tanggal mulai ad set
                // 'age_targeting', // Target umur audiens
                'attribution_setting', // Jendela atribusi yang digunakan
                'auction_bid', // Jumlah bid yang diajukan
                'auction_competitiveness', // Seberapa kompetitif iklan dalam lelang
                'auction_max_competitor_bid', // Perkiraan bid tertinggi dari kompetitor
                'average_purchases_conversion_value', // Rata-rata nilai konversi pembelian
                'buying_type', // Jenis pembelian iklan (misalnya: AUCTION, REACH_AND_FREQUENCY)
                'campaign_id', // ID kampanye
                'campaign_name', // Nama kampanye
                'clicks', // Jumlah total klik
                'conversions', // Jumlah total konversi
                'conversion_rate_ranking', // Peringkat rasio konversi dibanding iklan lain
                'cpc', // Biaya per klik
                'cpm', // Biaya per 1000 tayangan
                'cpp', // Biaya per hasil (Cost Per Purchase/Lead/etc.)
                'ctr', // Click-Through Rate (persentase klik dari tayangan)
                'date_start', // Tanggal mulai laporan
                'date_stop', // Tanggal akhir laporan
                'dda_results', // Data atribusi berbasis data (data-driven attribution)
                'frequency', // Rata-rata berapa kali audiens melihat iklan
                'impressions', // Total tayangan iklan
                'result_values_performance_indicator', // Indikator performa nilai hasil
                'spend', // Total biaya yang dikeluarkan
                'quality_ranking', // Peringkat kualitas iklan dibanding kompetitor
                'website_purchase_roas', // ROAS dari pembelian situs web
                'purchase_roas',        // ROAS dari semua pembelian
            ];
            // Parameter untuk permintaan API
            $params = [
                'time_range' => [
                    'since' => date('Y-m-01'), // First day of current month
                    'until' => date('Y-m-t')   // Last day of current month
                ],
                'level' => 'ad',             // Tingkat iklan
                'action_attribution_windows' => ['28d_click', '1d_view'], // Jendela atribusi
                'time_increment' => 'all_days', // Satu hasil untuk seluruh periode
                'use_account_attribution_setting' => true, // Gunakan pengaturan atribusi akun
                // 'limit' => 1000,             // Maksimal hasil per halaman
            ];
            // Dapatkan data insights
            $insights = $account->getInsights($insightFields, $params);
            // return response()->json([
            //     // 'success' => true,
            //     'data' => $insights,
            // ]);

            $insightData = [];

            // Iterasi melalui hasil
            // do {
            // return "sini";
            // foreach ($insights as $insight) {
            //     // Proses data pembelian situs web
            //     $websitePurchaseRoas = $this->extractActionValue($insight->website_purchase_roas, 'purchase');
            //     $purchaseRoas = $this->extractActionValue($insight->purchase_roas, 'purchase');
            //     $purchaseActions = $this->extractActionValue($insight->actions, 'purchase');
            //     $conversions = $this->extractActionValue($insight->conversions, 'purchase');

            //     $insightData[] = [
            //         'id_akun' => $insight->account_id ?? null,
            //         'nama_akun' => $insight->account_name ?? null,
            //         'id_iklan' => $insight->ad_id ?? null,
            //         'nama_iklan' => $insight->ad_name ?? null,
            //         'website_purchase_roas' => $websitePurchaseRoas, // ROAS pembelian situs web
            //         'purchase_roas' => $purchaseRoas,                // ROAS total pembelian
            //         'pembelian' => $purchaseActions,                 // Jumlah pembelian
            //         'konversi_pembelian' => $conversions,            // Konversi pembelian
            //         'biaya_dibelanjakan' => $insight->spend ?? null,
            //         'tayangan' => $insight->impressions ?? null,
            //         'klik' => $insight->clicks ?? null,
            //         'jangkauan' => $insight->reach ?? null,
            //     ];
            // }

            foreach ($insights as $insight) {
                $insightData[] = [
                    'mata_uang_akun' => $insight->account_currency ?? null, // Mata uang akun iklan
                    'id_akun' => $insight->account_id ?? null, // ID akun iklan
                    'nama_akun' => $insight->account_name ?? null, // Nama akun iklan
                    'nilai_aksi' => $insight->action_values ?? null, // Nilai konversi dari semua aksi (misalnya pembelian)
                    'aksi' => $insight->actions ?? null, // Semua jenis tindakan yang dilakukan user terhadap iklan
                    'klik_iklan' => $insight->ad_click_actions ?? null, // Jumlah klik pada iklan
                    'id_iklan' => $insight->ad_id ?? null, // ID iklan
                    'aksi_tayangan_iklan' => $insight->ad_impression_actions ?? null, // Tindakan saat tayangan iklan terjadi
                    'nama_iklan' => $insight->ad_name ?? null, // Nama iklan
                    'akhir_set_iklan' => $insight->adset_end ?? null, // Tanggal akhir ad set
                    'id_set_iklan' => $insight->adset_id ?? null, // ID ad set
                    'nama_set_iklan' => $insight->adset_name ?? null, // Nama ad set
                    'awal_set_iklan' => $insight->adset_start ?? null, // Tanggal mulai ad set
                    // 'target_usia' => $insight->age_targeting ?? null, // Target umur audiens
                    'pengaturan_atribusi' => $insight->attribution_setting ?? null, // Jendela atribusi yang digunakan
                    'penawaran_lelang' => $insight->auction_bid ?? null, // Jumlah bid yang diajukan
                    'daya_saing_lelang' => $insight->auction_competitiveness ?? null, // Seberapa kompetitif iklan dalam lelang
                    'maks_bid_kompetitor' => $insight->auction_max_competitor_bid ?? null, // Perkiraan bid tertinggi dari kompetitor
                    'rata_nilai_konversi' => $insight->average_purchases_conversion_value ?? null, // Rata-rata nilai konversi pembelian
                    'jenis_pembelian' => $insight->buying_type ?? null, // Jenis pembelian iklan (AUCTION, dll.)
                    'id_kampanye' => $insight->campaign_id ?? null, // ID kampanye
                    'nama_kampanye' => $insight->campaign_name ?? null, // Nama kampanye
                    'klik' => $insight->clicks ?? null, // Jumlah total klik
                    'konversi' => $insight->conversions ?? null, // Jumlah total konversi
                    'peringkat_rasio_konversi' => $insight->conversion_rate_ranking ?? null, // Dibanding iklan lain
                    'biaya_per_klik' => $insight->cpc ?? null, // Biaya per klik
                    'biaya_per_1000_tayangan' => $insight->cpm ?? null, // Biaya per 1000 tayangan
                    'biaya_per_hasil' => $insight->cpp ?? null, // Cost per result
                    'ctr' => $insight->ctr ?? null, // Click-through rate
                    'tanggal_mulai_laporan' => $insight->date_start ?? null, // Tanggal awal laporan
                    'tanggal_akhir_laporan' => $insight->date_stop ?? null, // Tanggal akhir laporan
                    'hasil_dda' => $insight->dda_results ?? null, // Data driven attribution
                    'frekuensi' => $insight->frequency ?? null, // Rata-rata jumlah tayangan per orang
                    'impresi' => $insight->impressions ?? null, // Jumlah total tayangan
                    'jangkauan' => $insight->reach ?? null, // Jumlah orang unik yang melihat iklan
                    'indikator_kinerja_nilai_hasil' => $insight->result_values_performance_indicator ?? null, // Indikator performa nilai hasil
                    'biaya_dibelanjakan' => $insight->spend ?? null, // Total biaya dikeluarkan
                    'peringkat_kualitas' => $insight->quality_ranking ?? null, // Peringkat kualitas dibanding iklan lain
                    'website_purchase_roas' => $insight->website_purchase_roas ?? null, // Peringkat kualitas dibanding iklan lain
                ];
            }

            // return $insightData;
            // } while ($insights = $insights->getNextPage());

            // Urutkan berdasarkan ID kampanye (opsional)
            // usort($insightData, function ($a, $b) {
            //     return $b['id_akun'] <=> $a['id_akun'];
            // });

            // Kembalikan hasil
            return response()->json([
                // 'success' => true,
                'data' => $insightData,
            ]);
        } catch (\Exception $e) {
            Log::info('Facebook Ads API Info: ' . $e->getMessage());
            // Log::error('Facebook Ads API Error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching data.',
            ], 500);
        }
    }

    public function getDataInsightsFileds($account)
    {
        try {
            $accountId = 'act_' . '****************';
            $account = new AdAccount($accountId);

            $fields = [
                InsightsResultFields::DESCRIPTION,
                InsightsResultFields::DESCRIPTION_FROM_API_DOC,
                InsightsResultFields::ID,
                InsightsResultFields::NAME,
                InsightsResultFields::PERIOD,
                InsightsResultFields::TITLE,
                InsightsResultFields::VALUES,
            ];

            $params = [
                'date_preset' => 'maximum',
                'limit' => 1000,
                'insights_selected_metrics' => 'cost_per_result',
            ];

            $insights = $account->getInsights($fields, $params); //InsightsResultFields.php

            $resultData = [];
            foreach ($insights as $val) {
                $valData = [
                    'DESCRIPTION' => $val->{InsightsResultFields::DESCRIPTION},
                    'DESCRIPTION_FROM_API_DOC' => $val->{InsightsResultFields::DESCRIPTION_FROM_API_DOC},
                    'ID' => $val->{InsightsResultFields::ID},
                    'NAME' => $val->{InsightsResultFields::NAME},
                    'PERIOD' => $val->{InsightsResultFields::PERIOD},
                    'TITLE' => $val->{InsightsResultFields::TITLE},
                    'VALUES' => $val->{InsightsResultFields::VALUES},
                ];

                $resultData[] = $valData;
            }


            // Kembalikan hasil
            return response()->json([
                // 'success' => true,
                'data' => $resultData,
            ]);
        } catch (\Exception $e) {
            Log::error('Facebook Ads API Error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(), // tampilkan error asli untuk debug
            ], 500);
        }

    }

    /**
     * Helper function untuk mengekstrak nilai berdasarkan action_type.
     */
    private function extractActionValue($actions, $actionType)
    {
        if (is_array($actions)) {
            foreach ($actions as $action) {
                if ($action->action_type === $actionType) {
                    return $action->value ?? null;
                }
            }
        }
        return null;
    }
}
