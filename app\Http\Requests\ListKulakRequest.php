<?php

namespace App\Http\Requests;

use App\Enums\KurirEnum;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class ListKulakRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "tanggal_kulak" => ["required", "date"],
            'product_id' => ['nullable', 'exists:products,id'],
            'nama_produk' => ['required', 'string', 'max:255'],
            'varian_produk' => ['required', 'array', 'min:1'],
            'varian_produk.*' => ['required', 'string', 'max:255'],
            'harga' => ['required', 'numeric', 'min:0'],
            'harga_reseller' => ['required', 'numeric', 'min:0'],
            'qty_cod' => ['required', 'integer', 'min:0'],
            'qty_tf' => ['required', 'integer', 'min:0'],
            'total_qty' => ['required', 'integer', 'min:0'],
            'total_kulak' => ['required', 'numeric', 'min:0'],
            'no_resi' => ['nullable', 'string', 'max:255'],
            'nama_kurir' => ['required', 'string', Rule::in(KurirEnum::values())],
            'status' => ['required', 'string', 'in:stok,stok_retur'],
            'link_produk' => ['required', 'array', 'min:1'],
            'link_produk.*' => ['required', 'url', 'max:255']
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'tanggal_kulak.required' => 'Tanggal kulak harus diisi',
            'tanggal_kulak.date' => 'Format tanggal tidak valid',
            'product_id.exists' => 'Produk yang dipilih tidak valid',
            'nama_produk.required' => 'Nama produk harus diisi',
            'nama_produk.max' => 'Nama produk maksimal 255 karakter',
            'varian_produk.required' => 'Variasi produk harus diisi',
            'varian_produk.array' => 'Format variasi produk tidak valid',
            'varian_produk.min' => 'Minimal 1 variasi produk harus diisi',
            'varian_produk.*.required' => 'Variasi produk tidak boleh kosong',
            'varian_produk.*.max' => 'Variasi produk maksimal 255 karakter',
            'harga.required' => 'Harga harus diisi',
            'harga.numeric' => 'Harga harus berupa angka',
            'harga.min' => 'Harga minimal 0',
            'harga_reseller.required' => 'Harga reseller harus diisi',
            'harga_reseller.numeric' => 'Harga reseller harus berupa angka',
            'harga_reseller.min' => 'Harga reseller minimal 0',
            'qty_cod.required' => 'Qty COD harus diisi',
            'qty_cod.integer' => 'Qty COD harus berupa angka bulat',
            'qty_cod.min' => 'Qty COD minimal 0',
            'qty_tf.required' => 'Qty TF harus diisi',
            'qty_tf.integer' => 'Qty TF harus berupa angka bulat',
            'qty_tf.min' => 'Qty TF minimal 0',
            'total_qty.required' => 'Total QTY harus diisi',
            'total_qty.integer' => 'Total QTY harus berupa angka bulat',
            'total_qty.min' => 'Total QTY minimal 0',
            'total_kulak.required' => 'Total kulak harus diisi',
            'total_kulak.numeric' => 'Total kulak harus berupa angka',
            'total_kulak.min' => 'Total kulak minimal 0',
            'no_resi.max' => 'Nomor resi maksimal 255 karakter',
            'nama_kurir.required' => 'Kurir harus dipilih',
            'nama_kurir.in' => 'Kurir tidak valid',
            'status.required' => 'Status harus dipilih',
            'status.in' => 'Status tidak valid',
            'link_produk.required' => 'Link produk harus diisi',
            'link_produk.array' => 'Format link produk tidak valid',
            'link_produk.min' => 'Minimal 1 link produk harus diisi',
            'link_produk.*.required' => 'Link produk tidak boleh kosong',
            'link_produk.*.url' => 'Format link produk tidak valid',
            'link_produk.*.max' => 'Link produk maksimal 255 karakter'
        ];
    }
}
