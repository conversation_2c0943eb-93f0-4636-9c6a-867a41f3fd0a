<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CprDataUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $accountId;
    public $campaignData;
    public $summary;
    public $timestamp;

    /**
     * Create a new event instance.
     */
    public function __construct(string $accountId, array $campaignData, array $summary = [])
    {
        $this->accountId = $accountId;
        $this->campaignData = $campaignData;
        $this->summary = $summary;
        $this->timestamp = now()->toDateTimeString();
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('cpr-monitoring'),
            new PrivateChannel('cpr-monitoring.' . $this->accountId),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'CprDataUpdated';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'accountId' => $this->accountId,
            'campaignData' => $this->campaignData,
            'summary' => $this->summary,
            'timestamp' => $this->timestamp,
            'message' => 'CPR data has been updated for account ' . $this->accountId
        ];
    }
}
