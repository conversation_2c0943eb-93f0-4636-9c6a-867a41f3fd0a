<?php

namespace App\Jobs\Import;

use App\Services\Order\OrderService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\WithStartRow;

class OrderExcelJob implements ShouldQueue, WithStartRow
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public function startRow(): int
    {
        return 2;
    }
    /**
     * Create a new job instance.
     */
    public function __construct(protected Collection $rows){}

    /**
     * Execute the job.
     */
    public function handle(OrderService $orderService): void
    {
        try {
            $chunks = $this->rows->chunk(100); // Pisahkan data menjadi 100 baris per chunk
            foreach ($chunks as $chunk) {
                $orderService->processRow($chunk);
            }
        } catch (\Exception $e) {
            // Log::error('Import dihentikan: ' . $e->getMessage());
            Log::error('Error Import: ' . $e);
            $this->fail($e); // Hentikan job queue
        }
    }

    public function failed(\Throwable $exception)
    {
        // Handle the failure, e.g., send a notification
        Log::error('Job failed: ' . $exception->getMessage());
    }
}
