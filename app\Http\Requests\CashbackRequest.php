<?php

namespace App\Http\Requests;

use App\Enums\LevelUser;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CashbackRequest extends FormRequest
{
    public $bulan;
    public $tahun;
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = $this->route('laporan.cashback');
        return [
            // 'bulanTahun' => ['required', 'string'],
            'bulanTahun' => ['required', 'string'],
            'nilai_cashback' => ['required', 'numeric'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'bulanTahun.required' => 'Bulan dan Tahun harus diisi',
            'bulanTahun.string' => 'Bulan dan Tahun harus berupa string',
            'nilai_cashback.required' => 'Nilai Cashback harus diisi',
            'nilai_cashback.numeric' => 'Nilai Cashback harus berupa angka',
        ];
    }
}
