<?php

namespace App\Http\Requests;

use App\Enums\LevelUser;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use MessageFormatter;

class DataMateriIklanRequest extends FormRequest
{
    public $bulan;
    public $tahun;
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // $id = $this->route('laporan.cashback');
        return [
            'judul_materi' => 'required',
            'deskripsi_iklan' => 'required',
            'hastag' => 'required',
            'headline' => 'required',
            'platform' => 'required',
            'status' => 'nullable|integer',            
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {           
        return [
            'judul_materi.required' => 'Judul Materi harus diisi',
            'deskripsi_iklan.required' => 'Deskripsi Iklan harus diisi',
            'hastag.required' => 'Hastag harus diisi',
            'headline.required' => 'Headline harus diisi',
            'platform.required' => 'Platform harus diisi',
            'status' => 'sometimes|nullable|integer'            
        ];
    }
}
