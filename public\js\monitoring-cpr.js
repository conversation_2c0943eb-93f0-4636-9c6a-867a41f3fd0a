class MonitoringCPR {
    constructor() {
        this.table = null;
        this.filters = {
            accountId: null,
        };
        this.cache = {
            data: null,
            timestamp: null,
            expiry: 5 * 60 * 1000, // 5 minutes
        };
        this.init();
    }

    init() {
        // Verify routes are available
        if (!this.verifyRoutes()) {
            this.showToast(
                "error",
                "Kesalahan konfigurasi aplikasi. Silakan hubungi dukungan teknis."
            );
            return;
        }

        this.addInitialStateGuidance();
        this.initializeDataTable();
        this.bindEvents();
        this.updateButtonState();
    }

    verifyRoutes() {
        const requiredRoutes = [
            "monitoringCprData",
            "monitoringCprCreate",
            "monitoringCprSync",
        ];
        return requiredRoutes.every((route) => window.routes[route]);
    }

    addInitialStateGuidance() {
        const guidance = `
            <div class="alert alert-info mb-3" role="alert">
                <i class="bx bx-info-circle me-2"></i>
                <PERSON><PERSON>an pilih akun iklan untuk mulai memantau performa kampanye.
            </div>
        `;
        $(".filter-section").prepend(guidance);
    }

    initializeDataTable() {
        if (!window.routes.monitoringCprData) {
            console.error("Data route not defined");
            return;
        }

        // Destroy existing table if it exists
        if ($.fn.DataTable.isDataTable("#tabelMasterCampaign")) {
            $("#tabelMasterCampaign").DataTable().destroy();
        }

        this.table = $("#tabelMasterCampaign").DataTable({
            processing: true,
            serverSide: true,
            deferLoading: 0, // Prevent initial load
            ajax: {
                url: window.routes.monitoringCprData,
                type: "GET",
                data: (d) => {
                    // Prevent AJAX request if no account is selected
                    if (!this.filters.accountId) {
                        return false;
                    }
                    d.idAkun = this.filters.accountId;
                    console.log("Fetching data with params:", d);
                    return d;
                },
                dataSrc: (response) => {
                    console.log("Received data:", response);
                    if (!response.data) {
                        console.error("No data received from server");
                        return [];
                    }
                    return response.data;
                },
                error: (xhr, error, thrown) => {
                    // Only show error if account is selected
                    if (this.filters.accountId) {
                        console.error("DataTable error:", error, thrown);
                        this.handleError(error, "loadData");
                    }
                },
            },
            columns: [
                {
                    data: null,
                    name: "DT_RowIndex",
                    orderable: false,
                    searchable: false,
                    width: "50px",
                    render: function (data, type, row, meta) {
                        return meta.row + meta.settings._iDisplayStart + 1;
                    },
                },
                {
                    data: "campaign_name",
                    name: "campaign_name",
                    render: (data) => {
                        if (!data) return '<span class="text-muted">-</span>';
                        return `<div class="campaign-name" title="${data}">${data}</div>`;
                    },
                },
                {
                    data: "status",
                    name: "status",
                    orderable: true,
                    searchable: true,
                },
                {
                    data: "results",
                    name: "results",
                    type: "num",
                    orderable: true,
                    searchable: false,
                    className: "text-center",
                    render: (data, type, row) => {
                        if (type === "display") {
                            return data; // Already formatted from server
                        }
                        return parseInt(data) || 0; // Use numeric value for sorting
                    },
                },
                {
                    data: "cpr",
                    name: "cpr",
                    type: "num",
                    title: "CPR (Cost per Result)",
                    orderable: true,
                    searchable: false,
                    className: "text-end",
                    render: (data, type, row) => {
                        if (type === "display") {
                            return data; // Already formatted from server
                        }
                        return row.cpr_raw || 0; // Use raw numeric value for sorting
                    },
                },
                {
                    data: "spend",
                    name: "spend",
                    type: "num",
                    title: "Amount Spent",
                    orderable: true,
                    searchable: false,
                    className: "text-end",
                    render: (data, type, row) => {
                        if (type === "display") {
                            return data; // Already formatted from server
                        }
                        return row.spend_raw || 0; // Use raw numeric value for sorting
                    },
                },
                {
                    data: "last_synced_display",
                    name: "last_synced_at",
                    title: "Last Sync",
                    orderable: true,
                    searchable: false,
                    className: "text-center",
                    render: (data, type, row) => {
                        if (type === "display") {
                            return data; // Already formatted from server
                        }
                        return row.last_synced_at || ""; // Use raw value for sorting
                    },
                },
            ],
            order: [[5, "desc"]], // Default sort by amount spent (spend column)
            pageLength: 25,
            lengthMenu: [
                [10, 25, 50, 100],
                [10, 25, 50, 100],
            ],
            language: {
                processing:
                    '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Memuat...</span></div></div>',
                emptyTable:
                    'Tidak ada data kampanye tersedia. Klik "Sinkronisasi Data" untuk memuat dari Facebook Ads.',
                zeroRecords:
                    "Tidak ada kampanye yang ditemukan untuk kriteria yang dipilih.",
                info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
                infoEmpty: "Menampilkan 0 sampai 0 dari 0 entri",
                infoFiltered: "(difilter dari _MAX_ total entri)",
                lengthMenu: "Tampilkan _MENU_ entri",
                search: "Cari:",
                paginate: {
                    first: "Pertama",
                    last: "Terakhir",
                    next: "Selanjutnya",
                    previous: "Sebelumnya",
                },
            },
            drawCallback: (settings) => {
                console.log("Table redrawn with data:", settings.json);
                console.log("Current order:", settings.aaSorting);
                this.updateTableAccessibility();
            },
            initComplete: (settings, json) => {
                console.log("Table initialization complete:", json);
                console.log("Initial order applied:", settings.aaSorting);
            },
        });

        // Add error handling for AJAX requests
        $(document).ajaxError((event, jqXHR, settings, error) => {
            console.error("AJAX Error:", {
                url: settings.url,
                status: jqXHR.status,
                error: error,
            });
        });
    }

    updateTableAccessibility() {
        // Add ARIA labels and roles
        $("#tabelMasterCampaign").attr("role", "grid");
        $("#tabelMasterCampaign thead").attr("role", "rowgroup");
        $("#tabelMasterCampaign tbody").attr("role", "rowgroup");
        $("#tabelMasterCampaign th").attr("role", "columnheader");
        $("#tabelMasterCampaign td").attr("role", "cell");
    }

    bindEvents() {
        // Account selection
        $("#idAkun").on("change", (e) => {
            this.filters.accountId = $(e.target).val();
            this.updateButtonState();
            this.clearValidationErrors();
            if (this.table && this.filters.accountId) {
                this.table.draw();
            }
        });

        // Sync data button
        $("#btnSyncData").on("click", (e) => {
            e.preventDefault();
            this.syncWithFacebook();
        });

        // Refresh table button
        $("#btnRefreshTable").on("click", (e) => {
            e.preventDefault();
            if (this.filters.accountId && this.table) {
                this.table.draw();
                this.showToast("info", "Tabel telah diperbarui");
            }
        });

        // Add keyboard navigation
        this.addKeyboardNavigation();
    }

    addKeyboardNavigation() {
        $(document).on("keydown", (e) => {
            if (e.ctrlKey && e.key === "r") {
                e.preventDefault();
                $("#btnRefreshTable").click();
            }
        });
    }

    updateButtonState() {
        const hasAccount = !!this.filters.accountId;
        $("#btnSyncData").prop("disabled", !hasAccount);
        $("#btnRefreshTable").prop("disabled", !hasAccount);
    }

    async syncWithFacebook() {
        if (!this.validateForm()) return;
        if (!window.routes.monitoringCprSync) {
            this.showToast("error", "Route sinkronisasi tidak terdefinisi");
            return;
        }

        const $btn = $("#btnSyncData");
        const originalText = $btn.find(".btn-text").text();

        try {
            this.setLoadingState($btn, "Menyinkronkan...");

            const accountId = this.filters.accountId;
            const accountName = $("#idAkun option:selected").text();

            // Get current month date range
            const now = new Date();
            const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
            const endOfMonth = new Date(
                now.getFullYear(),
                now.getMonth() + 1,
                0
            );

            const response = await $.ajax({
                url: window.routes.monitoringCprSync,
                method: "POST",
                data: {
                    idAkun: accountId,
                    namaAkun: accountName,
                    start_date: startOfMonth.toISOString().split("T")[0], // First day of current month
                    end_date: endOfMonth.toISOString().split("T")[0], // Last day of current month
                    _token: window.csrfToken,
                },
                error: (xhr, status, error) => {
                    const errorMessage = xhr.responseJSON?.message || error;
                    throw new Error(errorMessage);
                },
            });

            if (response.success) {
                if (this.table) {
                    this.table.draw();
                }

                let message = "Data berhasil disinkronkan dengan Facebook";
                if (response.stats) {
                    message += ` (${response.stats.total_campaigns} kampanye diproses)`;
                    if (
                        response.stats.date_range &&
                        response.stats.date_range.formatted
                    ) {
                        message += `\nRentang data: ${response.stats.date_range.formatted}`;
                    }
                }

                this.showToast("success", message);
                this.updateCache(response);
                this.updateDataRangeInfo(response.stats?.date_range);
            } else {
                throw new Error(response.message || "Sinkronisasi gagal");
            }
        } catch (error) {
            this.handleError(error, "syncWithFacebook");
        } finally {
            this.resetButtonState($btn, originalText);
        }
    }

    setLoadingState($btn, text) {
        $btn.prop("disabled", true)
            .addClass("btn-loading")
            .find(".btn-text")
            .text(text);
        $btn.find("i").addClass("bx-spin");
    }

    resetButtonState($btn, originalText) {
        $btn.prop("disabled", false)
            .removeClass("btn-loading")
            .find(".btn-text")
            .text(originalText);
        $btn.find("i").removeClass("bx-spin");
    }

    validateForm() {
        const accountId = this.filters.accountId;
        const errors = [];

        if (!accountId) {
            errors.push({
                field: "#idAkun",
                message: "Silakan pilih akun iklan",
            });
        }

        if (errors.length > 0) {
            errors.forEach((error) => {
                this.showValidationError(error.field, error.message);
            });
            return false;
        }

        this.clearValidationErrors();
        return true;
    }

    showValidationError(selector, message) {
        $(selector)
            .addClass("is-invalid")
            .siblings(".invalid-feedback")
            .html(message);
    }

    clearValidationErrors() {
        $(".is-invalid").removeClass("is-invalid");
        $(".invalid-feedback").html("");
    }

    getStatusClass(status) {
        switch (status) {
            case "active":
                return "status-active";
            case "paused":
                return "status-paused";
            default:
                return "status-inactive";
        }
    }

    handleError(error, operation) {
        let message = error.message || "Terjadi kesalahan. Silakan coba lagi.";
        let title = "Operasi Gagal";

        // Handle specific error types
        if (
            error.message &&
            error.message.includes("FACEBOOK_NOT_CONFIGURED")
        ) {
            title = "Facebook API Tidak Dikonfigurasi";
            message =
                "Kredensial Facebook API belum dikonfigurasi. Silakan hubungi administrator untuk mengatur integrasi Facebook.";
        } else if (
            error.message &&
            error.message.includes("Account ID is required")
        ) {
            title = "Kesalahan Validasi";
            message = "Silakan pilih akun iklan sebelum melanjutkan.";
        }

        Swal.fire({
            title: title,
            text: message,
            icon: "error",
            showCancelButton: operation !== "validateForm",
            confirmButtonText:
                operation !== "validateForm" ? "Coba Lagi" : "OK",
            cancelButtonText: "Tutup",
        }).then((result) => {
            if (result.isConfirmed && operation !== "validateForm") {
                this[operation]();
            }
        });
    }

    showToast(type, message) {
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
        });

        Toast.fire({
            icon: type,
            title: message,
        });
    }

    updateCache(data) {
        this.cache.data = data;
        this.cache.timestamp = Date.now();
    }

    isCacheValid() {
        return (
            this.cache.data &&
            this.cache.timestamp &&
            Date.now() - this.cache.timestamp < this.cache.expiry
        );
    }

    updateDataRangeInfo(dateRange) {
        if (!dateRange || !dateRange.formatted) return;

        // Remove existing range info
        $(".data-range-info").remove();

        // Add range info to filter section
        const rangeInfo = `
            <div class="alert alert-info data-range-info mt-2" role="alert">
                <i class="bx bx-calendar me-2"></i>
                <strong>Rentang Data Facebook API:</strong> ${dateRange.formatted}
                <small class="d-block text-muted mt-1">
                    Data yang ditampilkan mencakup periode dari ${dateRange.since} hingga ${dateRange.until}
                </small>
            </div>
        `;

        $(".filter-section .card-body").append(rangeInfo);
    }
}

// Initialize the application
$(document).ready(() => {
    new MonitoringCPR();
});
