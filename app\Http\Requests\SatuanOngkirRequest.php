<?php

namespace App\Http\Requests;

use App\Enums\LevelUser;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SatuanOngkirRequest extends FormRequest
{
    public $bulan;
    public $tahun;
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // $id = $this->route('laporan.cashback');
        return [
            'harga_satuan'      => ['required'],
            'ongkir'            => ['required'],
            'harga_jual'        => ['required'],
            'tag_konsumen'      => ['required'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'harga_satuan.required'     => 'Harga Satuan harus diisi',
            'ongkir.required'           => 'Ongkie harus diisi',
            'harga_jual.required'       => 'Harga Jual harus diisi',
            'tag_konsumen.required'     => 'Tag Konsumen harus diisi',
        ];
    }
}
