@extends('templates.app')
@section('title', $title)
@section('content')
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h6 class="mb-0 text-uppercase">{{ $title }}</h6>
            <small class="text-muted">Pantau dan analisis performa kampanye Facebook Ads</small>
        </div>
    </div>
    <hr />

    <!-- Filter Section -->
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="bx bx-filter me-2"></i>Filter & Manajemen Data
            </h6>
            <small class="text-muted">Pilih akun iklan dan rentang tanggal untuk melihat data CPR</small>
        </div>
        <div class="card-body filter-section">
            @if (empty($akun) || count($akun) === 0)
                <div class="alert alert-warning" role="alert">
                    <i class="bx bx-info-circle me-2"></i>
                    <strong>Tidak ada akun iklan yang tersedia.</strong>
                    Pastikan Facebook API sudah dikonfigurasi dan Anda memiliki akses ke akun iklan.
                </div>
            @else
                <div class="row g-3 align-items-end">
                    <div class="col-md-4">
                        <label for="idAkun" class="form-label">
                            Akun Iklan <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="idAkun" name="idAkun" required aria-label="Pilih akun iklan">
                            <option value="">Pilih akun...</option>
                            @foreach ($akun as $a)
                                <option value="{{ $a['id'] }}">{{ $a['nama'] }} ({{ $a['id'] }})</option>
                            @endforeach
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="col-md-8">
                        <button id="btnSyncData" class="btn btn-primary" disabled aria-label="Sinkronisasi data dengan Facebook">
                            <i class="bx bx-sync me-1" aria-hidden="true"></i>
                            <span class="btn-text">Sinkronisasi Data</span>
                        </button>
                        <button id="btnRefreshTable" class="btn btn-outline-secondary ms-2" disabled aria-label="Refresh data tabel">
                            <i class="bx bx-refresh" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Data Table Section -->
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="bx bx-chart-bar me-2"></i>Data Performa Kampanye
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="tabelMasterCampaign" class="table datatable table-striped table-hover" style="width:100%">
                    <thead>
                        <tr>
                            <th width="50" class="text-center">No</th>
                            <th width="300">Nama Kampanye</th>
                            <th width="100" class="text-center">Status</th>
                            <th width="100" class="text-center">Hasil</th>
                            <th width="180" class="text-end">
                                <i class="bx bx-calculator me-1"></i>CPR (Biaya per Hasil)
                                <small class="d-block text-muted">dalam IDR</small>
                            </th>
                            <th width="180" class="text-end">
                                <i class="bx bx-money me-1"></i>Jumlah Pengeluaran
                                <small class="d-block text-muted">dalam IDR</small>
                            </th>
                            <th width="120" class="text-center">
                                <i class="bx bx-time me-1"></i>Sinkronisasi Terakhir
                                <small class="d-block text-muted">Diperbarui</small>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data akan dimuat secara dinamis -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toastContainer"></div>

    <!-- Modal Container -->
    <div id="modalContainer"></div>
@endsection

@push('styles')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <link rel="stylesheet" href="{{ asset('css/monitoring-cpr.css') }}">
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Define routes for JavaScript
        window.routes = {
            monitoringCprData: "{{ route('ads.monitoring-cpr.data') }}",
            monitoringCprCreate: "{{ route('ads.monitoring-cpr.create') }}",
            monitoringCprSync: "{{ route('ads.monitoring-cpr.sync') }}"
        };

        // Define CSRF token
        window.csrfToken = "{{ csrf_token() }}";

        // Add error handling for missing routes
        Object.entries(window.routes).forEach(([key, value]) => {
            if (!value) {
                console.error(`Route ${key} is not defined`);
            }
        });

        // Log routes for debugging
        console.log('Routes:', window.routes);
        console.log('CSRF Token:', window.csrfToken);
    </script>
    <script src="{{ asset('js/monitoring-cpr.js') }}"></script>
@endpush
